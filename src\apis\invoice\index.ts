/*
 * @description:
 * @Author: lexy
 * @Date: 2023-08-16 15:30:44
 * @LastEditors: lexy
 * @LastEditTime: 2023-08-18 15:59:53
 */
import { get, post, put, del, patch } from '../http'

/**
 * @description 获取发票设置
 * @param
 * @returns
 */
export const doGetinvoiceSettings = (params: { invoiceSettingsClientType: 'SHOP' | 'SUPPLIER'; shopId: string }) => {
    return get({ url: `addon-invoice/invoice/invoiceSettings/`, params })
}
/**
 * @description 编辑发票设置
 * @param
 * @returns
 */
export const doPostinvoiceSettings = (data: any) => {
    return post({ url: `addon-invoice/invoice/invoiceSettings/`, data })
}
/**
 * @description 分页查询开票申请
 * @param
 * @returns
 */
export const doGetinvoiceRequestList = (params: any) => {
    return get({ url: `addon-invoice/invoice/invoiceRequest/`, params })
}
/**
 * @description 查询发票详情
 * @param
 * @returns
 */
export const doGetinvoiceDetail = (id: string) => {
    return get({ url: `addon-invoice/invoice/invoiceRequest/${id}` })
}
/**
 * @description 拒绝开票申请
 * @param
 * @returns
 */
export const doPostrefuseInvoiceRequest = (data: any) => {
    return post({ url: `addon-invoice/invoice/invoiceRequest/refuseInvoiceRequest`, data })
}
/**
 * @description 上传发票附件/重新上传发票附件
 * @param
 * @returns
 */
export const doPostinvoiceAttachment = (data: any) => {
    return post({ url: `addon-invoice/invoice/invoiceAttachment/upload `, data })
}

/**
 * @description 发票申请预检查
 * @param orderNo 订单号
 * @returns
 */
export const doGetInvoicePreRequest = (orderNo: string) => {
    return get({
        url: `addon-invoice/invoice/invoiceRequest/pre-request`,
        params: { orderNo },
        baseURL: 'http://*************:9999/'
    })
}
